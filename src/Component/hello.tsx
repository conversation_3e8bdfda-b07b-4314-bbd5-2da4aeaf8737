import {
  Flex,
  <PERSON><PERSON><PERSON>utton,
  <PERSON>,
  <PERSON><PERSON>,
  useDisclosure,
  Box,
  Stack,
  Collapsible,
} from "@chakra-ui/react";
import { useState } from "react";
import { FaChevronDown } from "react-icons/fa";
import { FiMenu as HamburgerIcon } from "react-icons/fi";

const Navbar = () => {
  const { open, onToggle } = useDisclosure();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const navItems = [
    { name: "Home", link: "/" },
    {
      name: "Destinations",
      subItems: ["Serengeti", "Zanzibar", "Kilimanjaro"],
    },
    {
      name: "Tours",
      subItems: ["Safari", "Beach", "Cultural"],
    },
    { name: "Activities", link: "#activities" },
    { name: "About", link: "#about" },
    { name: "Book Now", link: "#booking" },
  ];

  const toggleDropdown = (name: string) => {
    setActiveDropdown(activeDropdown === name ? null : name);
  };

  return (
    <Flex
      as="nav"
      bg="white"
      p={4}
      boxShadow="md"
      position="sticky"
      top={0}
      zIndex={10}
    >
      <Flex
        justify="space-between"
        align="center"
        w="100%"
        maxW="1200px"
        mx="auto"
      >
        <Link href="/" fontSize="2xl" fontFamily="heading" fontWeight="bold">
          Safari Tanzania
        </Link>

        {/* Desktop Navigation */}
        <Flex display={["none", "none", "flex"]} gap={6} align="center">
          {navItems.map((item) =>
            item.subItems ? (
              <Box key={item.name} position="relative">
                <Button
                  variant="ghost"
                  _hover={{ color: "brand.green" }}
                  onClick={() => toggleDropdown(item.name)}
                >
                  {item.name}
                  <FaChevronDown
                    style={{
                      transform:
                        activeDropdown === item.name ? "rotate(180deg)" : "",
                      transition: "transform 0.2s",
                    }}
                  />
                </Button>

                <Collapsible.Root open={activeDropdown === item.name}>
                  <Stack
                    position="absolute"
                    bg="white"
                    boxShadow="md"
                    p={2}
                    borderRadius="md"
                    minW="200px"
                  >
                    {item.subItems.map((sub) => (
                      <Link
                        key={sub}
                        href={`#${sub.toLowerCase()}`}
                        px={3}
                        py={2}
                        _hover={{ bg: "gray.100" }}
                      >
                        {sub}
                      </Link>
                    ))}
                  </Stack>
                </Collapsible.Root>
              </Box>
            ) : (
              <Link
                key={item.name}
                href={item.link}
                px={2}
                py={1}
                _hover={{ textDecoration: "none", color: "brand.green" }}
              >
                {item.name}
              </Link>
            )
          )}
        </Flex>

        {/* Mobile Navigation */}
        <IconButton
          display={["flex", "flex", "none"]}
          onClick={onToggle}
          aria-label="Open menu"
          variant="outline"
        >
          <HamburgerIcon />
        </IconButton>
      </Flex>

      {/* Mobile Menu */}
      <Collapsible.Root open={open}>
        <Stack
          display={["flex", "flex", "none"]}
          position="absolute"
          top="100%"
          left={0}
          right={0}
          bg="white"
          boxShadow="md"
          p={4}
        >
          {navItems.map((item) => (
            <Box key={item.name}>
              {item.subItems ? (
                <>
                  <Button
                    w="full"
                    justifyContent="space-between"
                    variant="ghost"
                    onClick={() => toggleDropdown(item.name)}
                  >
                    {item.name}
                    <FaChevronDown
                      style={{
                        transform:
                          activeDropdown === item.name ? "rotate(180deg)" : "",
                        transition: "transform 0.2s",
                      }}
                    />
                  </Button>
                  <Collapsible.Content hidden={activeDropdown !== item.name}>
                    <Stack pl={4}>
                      {item.subItems.map((sub) => (
                        <Link
                          key={sub}
                          href={`#${sub.toLowerCase()}`}
                          px={2}
                          py={1}
                          _hover={{ color: "brand.green" }}
                        >
                          {sub}
                        </Link>
                      ))}
                    </Stack>
                  </Collapsible.Content>
                </>
              ) : (
                <Link
                  href={item.link}
                  display="block"
                  px={2}
                  py={1}
                  _hover={{ color: "brand.green" }}
                >
                  {item.name}
                </Link>
              )}
            </Box>
          ))}
        </Stack>
      </Collapsible.Root>
    </Flex>
  );
};

export default Navbar;
